package com.zsmall.marketplace.domain.bo;

import com.hengjian.common.mybatis.core.page.PageQuery;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 请求体-Marketplace商品查询
 *
 * <AUTHOR>
 */
@Data
public class MpProductSearchBo extends PageQuery {

    private static final long serialVersionUID = 1L;

    /**
     * 搜索关键字
     */
    private String queryValue;

    /**
     * 分类id集合
     */
    private List<Long> categoryIdList;

    /**
     * 固定标签:New Arrivals,Best Sales
     */
    private String fixedLabel;

    /**
     * 指定活动类型:StockLock,Buyout
     */
    private String activityType;

    /**
     * 标签id集合
     */
    private List<Long> labelIdList;

    /**
     * 最低价格
     */
    private BigDecimal minPrice;

    /**
     * 最高价格
     */
    private BigDecimal maxPrice;

    /**
     * 最快到达时间(天)
     */
    private Integer shippingDayMin;

    /**
     * 最慢到达时间(天)
     */
    private Integer shippingDayMax;

    /**
     * 渠道集合
     */
    private List<String> channelList;

    /**
     * 支持的物流，0-仅支持代发，1-仅支持自提，2-都支持
     */
    private List<Integer> supportedLogistics;

    /**
     * 排序类型(Newest;PriceAsc;PriceDesc;StockAsc;StockDesc)
     */
    private String sortType;
    /**
     * 站点
     */
    private String site;

}
