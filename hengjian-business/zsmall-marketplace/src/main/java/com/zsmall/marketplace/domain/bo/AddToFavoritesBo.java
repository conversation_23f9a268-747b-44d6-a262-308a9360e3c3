package com.zsmall.marketplace.domain.bo;

import com.zsmall.common.constant.ValidationMessage;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 请求体-加入收藏夹
 *
 * <AUTHOR>
 * @date 2023/8/16
 */
@Data
public class AddToFavoritesBo implements Serializable {

    private static final long serialVersionUID = 1L;

    private String productCode;

    private String productSkuCode;

    @NotNull(message = ValidationMessage.API_REQUIRED)
    private Boolean collected;
    //收藏类型 true是ProductCode,false是ProductSkuCode
    private Boolean isProductCode;
}
