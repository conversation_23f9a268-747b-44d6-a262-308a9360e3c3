package com.zsmall.product.entity.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hengjian.common.mybatis.core.domain.NoDeptBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警消息对象 warning_message
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("warning_message")
public class WarningMessage extends NoDeptBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 可见范围分销商/供应商/admin
     */
    private String tenantType;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 内容
     */
    private String content;


    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private Integer delFlag;

}
