package com.zsmall.product.biz.mq;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.rabbitmq.client.Channel;
import com.zsmall.product.biz.service.CanalStockWarningSupper;
import com.zsmall.product.entity.domain.mq.CanalStockWarningMqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Arrays;

/**
 * Canal库存预警消息监听器
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class CanalStockWarningRabbitMQListener {

    private final CanalStockWarningSupper canalStockWarningSupper;

    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstant.CANAL_STOCK_WARNING_QUEUE,
        concurrency = "1",
        containerFactory = "canalListenerContainerFactory")
    public void handleStockWarningMessage(Message message, @Header(AmqpHeaders.CHANNEL) Channel channel) throws IOException {
        try {
            log.info("接收到Canal库存预警消息: {}", new String(message.getBody()));

            // 处理消息
            canalStockWarningSupper.dealCanalStockWarningMessage(message, channel, false);

            // 手动确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);

        } catch (Exception e) {
            log.error("处理Canal库存预警消息失败", e);

            try {
                // 记录异常信息
                recordProcessingError(message, e);

                // 确认消息，避免重复处理
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);

            } catch (Exception ex) {
                log.error("记录Canal库存预警消息处理异常失败", ex);
                // 最终确认消息
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
        }
    }

    /**
     * 记录处理异常信息
     */
    private void recordProcessingError(Message message, Exception e) {
        try {
            String messageContext = new String(message.getBody());
            CanalStockWarningMqDTO canalStockWarningDTO = JSONUtil.toBean(messageContext, CanalStockWarningMqDTO.class);

            String errorInfo = String.format(
                "处理Canal库存预警消息失败 - 数据库: %s, 表名: %s, 操作类型: %s, 原因: %s, 堆栈信息: %s",
                ObjectUtil.isNotNull(canalStockWarningDTO) ? canalStockWarningDTO.getDatabase() : "unknown",
                ObjectUtil.isNotNull(canalStockWarningDTO) ? canalStockWarningDTO.getTable() : "unknown",
                ObjectUtil.isNotNull(canalStockWarningDTO) ? canalStockWarningDTO.getType() : "unknown",
                e.getCause() != null ? e.getCause().getMessage() : e.getMessage(),
                Arrays.toString(e.getStackTrace())
            );

            log.error("Canal库存预警消息处理异常详情: {}", errorInfo);

            // TODO: 可以考虑将异常信息存储到数据库中，便于后续分析和处理

        } catch (Exception ex) {
            log.error("记录Canal库存预警消息异常信息失败，原始异常: {}，记录异常: {}，消息体: {}",
                     e.getMessage(), ex.getMessage(), message, ex);
        }
    }
}
