package com.zsmall.product.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zsmall.product.biz.service.StockQueryService;
import com.zsmall.product.entity.domain.ProductSkuStock;
import com.zsmall.product.entity.iservice.IProductSkuStockService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 统一库存查询服务实现
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class StockQueryServiceImpl implements StockQueryService {

    private final IProductSkuStockService productSkuStockService;

    /**
     * 查询商品SKU在指定仓库的自提库存数量
     */
    @Override
    public Integer getPickupStockQuantity(String productSkuCode, String warehouseSystemCode) {
        if (StrUtil.isBlank(productSkuCode) || StrUtil.isBlank(warehouseSystemCode)) {
            log.warn("查询库存参数为空: productSkuCode={}, warehouseSystemCode={}", productSkuCode, warehouseSystemCode);
            return 0;
        }

        try {
            LambdaQueryWrapper<ProductSkuStock> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(ProductSkuStock::getProductSkuCode, productSkuCode)
                       .eq(ProductSkuStock::getWarehouseSystemCode, warehouseSystemCode)
                       .eq(ProductSkuStock::getDelFlag, "0");

            ProductSkuStock stock = productSkuStockService.getOne(queryWrapper);
            if (ObjectUtil.isNull(stock)) {
                log.debug("未找到库存记录: productSkuCode={}, warehouseSystemCode={}", productSkuCode, warehouseSystemCode);
                return 0;
            }

            Integer stockTotal = stock.getStockTotal();
            return ObjectUtil.isNull(stockTotal) ? 0 : Math.max(stockTotal, 0);

        } catch (Exception e) {
            log.error("查询库存异常: productSkuCode={}, warehouseSystemCode={}", productSkuCode, warehouseSystemCode, e);
            return 0;
        }
    }

    /**
     * 批量查询商品SKU的自提库存数量
     */
    @Override
    public Map<String, Integer> batchGetPickupStockQuantity(List<String> productSkuCodes, String warehouseSystemCode) {
        Map<String, Integer> result = new HashMap<>();

        if (CollUtil.isEmpty(productSkuCodes) || StrUtil.isBlank(warehouseSystemCode)) {
            log.warn("批量查询库存参数为空: productSkuCodes={}, warehouseSystemCode={}", productSkuCodes, warehouseSystemCode);
            return result;
        }

        try {
            LambdaQueryWrapper<ProductSkuStock> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(ProductSkuStock::getProductSkuCode, productSkuCodes)
                       .eq(ProductSkuStock::getWarehouseSystemCode, warehouseSystemCode)
                       .eq(ProductSkuStock::getDelFlag, "0");

            List<ProductSkuStock> stockList = productSkuStockService.list(queryWrapper);

            // 转换为Map
            Map<String, Integer> stockMap = stockList.stream()
                .collect(Collectors.toMap(
                    ProductSkuStock::getProductSkuCode,
                    stock -> ObjectUtil.isNull(stock.getStockTotal()) ? 0 : Math.max(stock.getStockTotal(), 0),
                    (existing, replacement) -> existing
                ));

            // 确保所有请求的SKU都有返回值
            for (String skuCode : productSkuCodes) {
                result.put(skuCode, stockMap.getOrDefault(skuCode, 0));
            }

        } catch (Exception e) {
            log.error("批量查询库存异常: productSkuCodes={}, warehouseSystemCode={}", productSkuCodes, warehouseSystemCode, e);
            // 异常情况下，返回所有SKU库存为0
            for (String skuCode : productSkuCodes) {
                result.put(skuCode, 0);
            }
        }

        return result;
    }

    /**
     * 查询商品SKU的代发库存状态
     */
    @Override
    public Integer getDropShippingStockQuantity(String productSkuCode, String warehouseSystemCode) {
        if (StrUtil.isBlank(productSkuCode) || StrUtil.isBlank(warehouseSystemCode)) {
            log.warn("查询代发库存参数为空: productSkuCode={}, warehouseSystemCode={}", productSkuCode, warehouseSystemCode);
            return 0;
        }

        try {
            LambdaQueryWrapper<ProductSkuStock> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(ProductSkuStock::getProductSkuCode, productSkuCode)
                       .eq(ProductSkuStock::getWarehouseSystemCode, warehouseSystemCode)
                       .eq(ProductSkuStock::getDelFlag, "0");

            ProductSkuStock stock = productSkuStockService.getOne(queryWrapper);
            if (ObjectUtil.isNull(stock)) {
                log.debug("未找到库存记录: productSkuCode={}, warehouseSystemCode={}", productSkuCode, warehouseSystemCode);
                return 0;
            }

            Integer dropShippingStockAvailable = stock.getDropShippingStockAvailable();
            Integer stockTotal = stock.getStockTotal();

            // 根据代发库存标识计算代发库存
            if (ObjectUtil.isNull(dropShippingStockAvailable) || dropShippingStockAvailable == 0) {
                // 单仓模式，代发库存为0
                return 0;
            } else {
                // 非单仓模式，代发库存等于自提库存
                return ObjectUtil.isNull(stockTotal) ? 0 : Math.max(stockTotal, 0);
            }

        } catch (Exception e) {
            log.error("查询代发库存异常: productSkuCode={}, warehouseSystemCode={}", productSkuCode, warehouseSystemCode, e);
            return 0;
        }
    }
}
