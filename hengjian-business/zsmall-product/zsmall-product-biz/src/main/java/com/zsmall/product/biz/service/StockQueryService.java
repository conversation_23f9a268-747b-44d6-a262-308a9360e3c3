package com.zsmall.product.biz.service;

/**
 * 统一库存查询服务接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface StockQueryService {

    /**
     * 查询商品SKU在指定仓库的自提库存数量
     *
     * @param productSkuCode SKU编码
     * @param warehouseSystemCode 仓库系统编码
     * @return 库存数量，0表示无库存，>0表示有库存
     */
    Integer getPickupStockQuantity(String productSkuCode, String warehouseSystemCode);

    /**
     * 批量查询商品SKU的自提库存数量
     *
     * @param productSkuCodes SKU编码列表
     * @param warehouseSystemCode 仓库系统编码
     * @return SKU编码与库存数量的映射
     */
    java.util.Map<String, Integer> batchGetPickupStockQuantity(java.util.List<String> productSkuCodes, String warehouseSystemCode);

    /**
     * 查询商品SKU的代发库存状态
     * 根据drop_shipping_stock_available和stock_total计算实际代发库存
     *
     * @param productSkuCode SKU编码
     * @param warehouseSystemCode 仓库系统编码
     * @return 代发库存数量，0表示无库存，>0表示有库存
     */
    Integer getDropShippingStockQuantity(String productSkuCode, String warehouseSystemCode);

}
